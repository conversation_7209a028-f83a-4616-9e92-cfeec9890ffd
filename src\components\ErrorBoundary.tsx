import { useRouteError } from "react-router";

export default function ErrorBoundary() {
  const error = useRouteError() as Error;

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-red-600">
          Something went wrong
        </h1>
        <p className="mt-2 text-gray-600">
          {error?.message || "An unexpected error occurred"}
        </p>
      </div>
    </div>
  );
}
