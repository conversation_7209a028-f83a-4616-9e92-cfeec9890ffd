{"name": "optiwise-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host --port 3000", "start": "vite --port 3000", "build": "tsc -b && vite build", "preview": "vite preview", "pretty": "prettier --write \"**/*.{js,jsx,ts,tsx,json}\"", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint --fix .", "tsc": "tsc", "prepare": "husky"}, "dependencies": {"@tailwindcss/vite": "^4.1.13", "clsx": "^2.1.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router": "^7.9.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.13"}, "devDependencies": {"@eslint/js": "^9.33.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/node": "^24.4.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react-swc": "^4.0.0", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.6", "prettier": "^3.5.2", "prettier-plugin-tailwindcss": "^0.6.11", "tw-animate-css": "^1.2.9", "typescript": "~5.4.4", "typescript-eslint": "^8.30.1", "vite": "^7.1.2"}}