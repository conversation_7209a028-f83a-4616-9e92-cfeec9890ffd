import { createBrowserRouter, RouterProvider } from "react-router";
import ErrorBoundary from './components/ErrorBoundary';
import RootLayout from './layouts/RootLayout';
import { routes } from './routes';

const router = createBrowserRouter([
  {
    path: '/',
    element: <RootLayout />,
    errorElement: <ErrorBoundary />,
    children: routes,
  },
]);

export default function App() {
  return <RouterProvider router={router} />;
}
