# OptiWise Frontend

A modern React application built with industry-standard practices and the latest technologies.

##  Tech Stack

- **React 19** - Latest React with concurrent features
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and dev server
- **React Router v7** - Modern routing with data router pattern
- **Tailwind CSS** - Utility-first CSS framework
- **ESLint + Prettier** - Code quality and formatting
- **Husky** - Git hooks for code quality

##  Project Structure

```
src/
├── components/          # Reusable UI components
├── constants/          # App constants and configuration
├── hooks/              # Custom React hooks
├── layouts/            # Layout components
├── lib/                # Utility functions and configurations
├── pages/              # Page components
├── types/              # TypeScript type definitions
└── main.tsx           # App entry point
```

##  Development

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Set up environment:**
   ```bash
   cp .env.example .env
   ```

3. **Start development server:**
   ```bash
   npm run dev
   ```

##  Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run pretty` - Format code with Prettier
- `npm run tsc` - Type check

##  Architecture Decisions

### Routing
- Uses React Router v7 data router pattern
- Nested layouts for consistent UI structure
- Error boundaries for graceful error handling

### State Management
- Custom hooks for business logic
- React's built-in state for local component state
- Ready for external state management (Redux Toolkit, Zustand)

### Styling
- Tailwind CSS for rapid UI development
- Utility functions for conditional classes
- Responsive design patterns

### Code Quality
- TypeScript for type safety
- ESLint with Airbnb configuration
- Prettier for consistent formatting
- Husky for pre-commit hooks
