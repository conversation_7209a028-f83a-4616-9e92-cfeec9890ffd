import type { RouteObject } from "react-router";

// import { lazyLoad } from "../../lib/lazy";
// const AccountingLayout = lazyLoad(() => import("../../layouts/AccountingLayout"));
// const Ledger = lazyLoad(() => import("../../pages/accounting/Ledger"));
// const Reports = lazyLoad(() => import("../../pages/accounting/Reports"));
// const Transactions = lazyLoad(() => import("../../pages/accounting/Transactions"));

export const accountingRoutes: RouteObject[] = [
  // {
  //   path: "/accounting",
  //   element: <AccountingLayout />,
  //   children: [
  //     { path: "ledger", element: <Ledger /> },
  //     { path: "reports", element: <Reports /> },
  //     { path: "transactions", element: <Transactions /> },
  //   ],
  // },
];
