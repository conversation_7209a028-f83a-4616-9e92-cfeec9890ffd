import type { RouteObject } from "react-router";
// import { lazyLoad } from "../../lib/lazy";

// const SalesLayout = lazyLoad(() => import("../../layouts/SalesLayout"));
// const Orders = lazyLoad(() => import("../../pages/sales/Orders"));
// const Customers = lazyLoad(() => import("../../pages/sales/Customers"));
// const Invoices = lazyLoad(() => import("../../pages/sales/Invoices"));

export const salesRoutes: RouteObject[] = [
  // {
  //   path: "/sales",
  //   element: <SalesLayout />,
  //   children: [
  //     { path: "orders", element: <Orders /> },
  //     { path: "customers", element: <Customers /> },
  //     { path: "invoices", element: <Invoices /> },
  //   ],
  // },
];
