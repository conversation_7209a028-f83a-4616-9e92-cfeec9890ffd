import { useState } from "react";

import type { AuthState, User } from "../types";

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: false,
  });

  const login = async (email: string, password: string) => {
    setAuthState((prev) => ({ ...prev, isLoading: true }));
    try {
      // Mock login - replace with actual API call
      const user: User = { id: "1", email, name: "User" };
      setAuthState({ user, isAuthenticated: true, isLoading: false });
    } catch {
      setAuthState((prev) => ({ ...prev, isLoading: false }));
    }
  };

  const logout = () => {
    setAuthState({ user: null, isAuthenticated: false, isLoading: false });
  };

  return { ...authState, login, logout };
}
