import type { RouteObject } from "react-router";
// import { lazyLoad } from "../../lib/lazy";

// const InventoryLayout = lazyLoad(() => import("../../layouts/InventoryLayout"));
// const Products = lazyLoad(() => import("../../pages/inventory/Products"));
// const Stock = lazyLoad(() => import("../../pages/inventory/Stock"));
// const Warehouses = lazyLoad(() => import("../../pages/inventory/Warehouses"));

export const inventoryRoutes: RouteObject[] = [
  // {
  //   path: "/inventory",
  //   element: <InventoryLayout />,
  //   children: [
  //     { path: "products", element: <Products /> },
  //     { path: "stock", element: <Stock /> },
  //     { path: "warehouses", element: <Warehouses /> },
  //   ],
  // },
];
