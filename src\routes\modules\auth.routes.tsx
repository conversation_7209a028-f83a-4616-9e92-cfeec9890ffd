import type { RouteObject } from "react-router";
import { lazyLoad } from "../../lib/lazy";
import AuthLayout from "../../layouts/AuthLayout";

const Login = lazyLoad(() => import("../../pages/auth/Login"));
const Register = lazyLoad(() => import("../../pages/auth/Register"));

export const authRoutes: RouteObject[] = [
  {
    path: "/auth",
    element: <AuthLayout />,
    children: [
      {
        path: "login",
        element: <Login />,
      },
      {
        path: "register",
        element: <Register />,
      },
    ],
  },
];
